{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - WordPress Configuration -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fab fa-wordpress me-2"></i>{% trans "WordPress Integration" %}</h4>
                    <p class="text-muted">{% trans "Configure your WordPress careers page" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- WordPress Setup Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-cog me-2"></i>
                            {% trans "WordPress Setup" %}
                        </h5>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Choose your preferred WordPress integration method" %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="wpMethod" class="form-label">{% trans "Integration Method" %}</label>
                            <select class="form-select" id="wpMethod" onchange="updateWPMethod()">
                                <option value="shortcode">{% trans "Shortcode (Recommended)" %}</option>
                                <option value="widget">{% trans "WordPress Widget" %}</option>
                                <option value="plugin">{% trans "Custom Plugin" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Company Branding Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Branding" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="Your Company">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Join our amazing team">
                        </div>
                    </div>

                    <!-- Design Settings Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design Settings" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="wpThemeStyle" class="form-label">{% trans "WordPress Theme Style" %}</label>
                            <select class="form-select" id="wpThemeStyle">
                                <option value="inherit">{% trans "Inherit from Theme" %}</option>
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="responsiveDesign" checked>
                            <label class="form-check-label" for="responsiveDesign">
                                {% trans "Responsive Design" %}
                            </label>
                        </div>
                    </div>

                    <!-- Content Settings Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-list me-2"></i>
                            {% trans "Content Settings" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="jobsPerPage" class="form-label">{% trans "Jobs Per Page" %}</label>
                            <select class="form-select" id="jobsPerPage">
                                <option value="5">5 {% trans "jobs" %}</option>
                                <option value="10" selected>10 {% trans "jobs" %}</option>
                                <option value="15">15 {% trans "jobs" %}</option>
                                <option value="all">{% trans "All jobs" %}</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showFilters" checked>
                            <label class="form-check-label" for="showFilters">
                                {% trans "Show Job Filters" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showSearch" checked>
                            <label class="form-check-label" for="showSearch">
                                {% trans "Show Search Box" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showPagination" checked>
                            <label class="form-check-label" for="showPagination">
                                {% trans "Show Pagination" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Code Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateWPCode()">
                            <i class="fab fa-wordpress me-2"></i>
                            {% trans "Generate WordPress Code" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Preview and Instructions -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Preview & Instructions" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="showTab('preview')">
                            <i class="fas fa-eye"></i> {% trans "Preview" %}
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showTab('instructions')">
                            <i class="fas fa-book"></i> {% trans "Instructions" %}
                        </button>
                    </div>
                </div>

                <!-- Preview Tab -->
                <div id="previewTab" class="tab-content active">
                    <div class="preview-container">
                        <div class="preview-frame">
                            <div id="wpPreview" class="wp-preview">
                                <!-- WordPress preview will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions Tab -->
                <div id="instructionsTab" class="tab-content">
                    <div class="instructions-container">
                        <div class="instruction-section" id="shortcodeInstructions">
                            <h5><i class="fas fa-code me-2"></i>{% trans "Shortcode Integration" %}</h5>
                            <div class="alert alert-success">
                                <strong>{% trans "Recommended Method" %}</strong> - {% trans "Easy to use and works with any WordPress theme" %}
                            </div>
                            <ol class="instruction-list">
                                <li>{% trans "Copy the shortcode below" %}</li>
                                <li>{% trans "Go to your WordPress admin panel" %}</li>
                                <li>{% trans "Edit the page where you want to display jobs" %}</li>
                                <li>{% trans "Paste the shortcode in the content area" %}</li>
                                <li>{% trans "Save and publish the page" %}</li>
                            </ol>
                            <div class="code-block">
                                <code id="shortcodeCode">[workloupe_careers]</code>
                                <button class="btn btn-sm btn-outline-secondary" onclick="copyCode('shortcodeCode')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        <div class="instruction-section" id="widgetInstructions" style="display: none;">
                            <h5><i class="fas fa-puzzle-piece me-2"></i>{% trans "WordPress Widget" %}</h5>
                            <div class="alert alert-info">
                                {% trans "Perfect for sidebars and widget areas" %}
                            </div>
                            <ol class="instruction-list">
                                <li>{% trans "Go to Appearance > Widgets in your WordPress admin" %}</li>
                                <li>{% trans "Find the 'Workloupe Careers' widget" %}</li>
                                <li>{% trans "Drag it to your desired widget area" %}</li>
                                <li>{% trans "Configure the widget settings" %}</li>
                                <li>{% trans "Save the widget" %}</li>
                            </ol>
                        </div>

                        <div class="instruction-section" id="pluginInstructions" style="display: none;">
                            <h5><i class="fas fa-plug me-2"></i>{% trans "Custom Plugin" %}</h5>
                            <div class="alert alert-warning">
                                {% trans "Advanced option - requires technical knowledge" %}
                            </div>
                            <ol class="instruction-list">
                                <li>{% trans "Download the custom plugin file" %}</li>
                                <li>{% trans "Upload it to your WordPress plugins directory" %}</li>
                                <li>{% trans "Activate the plugin in WordPress admin" %}</li>
                                <li>{% trans "Configure the plugin settings" %}</li>
                                <li>{% trans "Use shortcodes or widgets as needed" %}</li>
                            </ol>
                            <button class="btn btn-success" onclick="downloadWPPlugin()">
                                <i class="fas fa-download me-2"></i>
                                {% trans "Download Plugin" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>