{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - Customization Controls -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-file-code me-2"></i>{% trans "Full Page Builder" %}</h4>
                    <p class="text-muted">{% trans "Create a complete careers page" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- Company Information Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Information" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="Your Company">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Build what matters. Grow your career. Shape the future.">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyDescription" class="form-label">{% trans "Company Description" %}</label>
                            <textarea class="form-control" id="companyDescription" rows="3" placeholder="{% trans 'Describe your company' %}">We are a leading technology company focused on innovation and excellence.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                            <input type="file" class="form-control" id="logoUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 300x120px, PNG or JPG" %}</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="bannerUpload" class="form-label">{% trans "Header Banner" %}</label>
                            <input type="file" class="form-control" id="bannerUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 1200x400px, JPG or PNG" %}</small>
                        </div>
                    </div>

                    <!-- Design Customization Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design & Colors" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="secondaryColor" class="form-label">{% trans "Secondary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#6c757d">
                        </div>

                        <div class="form-group mb-3">
                            <label for="backgroundColor" class="form-label">{% trans "Background Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="backgroundColor" value="#f8f9fa">
                        </div>

                        <div class="form-group mb-3">
                            <label for="pageStyle" class="form-label">{% trans "Page Style" %}</label>
                            <select class="form-select" id="pageStyle">
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                                <option value="corporate">{% trans "Corporate" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Company Stats Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            {% trans "Company Statistics" %}
                        </h5>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="teamSize" class="form-label">{% trans "Team Size" %}</label>
                                    <input type="text" class="form-control" id="teamSize" placeholder="250+" value="250+">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="countries" class="form-label">{% trans "Countries" %}</label>
                                    <input type="text" class="form-control" id="countries" placeholder="15+" value="15+">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="satisfaction" class="form-label">{% trans "Satisfaction" %}</label>
                                    <input type="text" class="form-control" id="satisfaction" placeholder="98%" value="98%">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="openPositions" class="form-label">{% trans "Open Positions" %}</label>
                                    <input type="text" class="form-control" id="openPositions" placeholder="25+" value="25+">
                                </div>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showStats" checked>
                            <label class="form-check-label" for="showStats">
                                {% trans "Show Company Statistics" %}
                            </label>
                        </div>
                    </div>

                    <!-- Company Values Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-heart me-2"></i>
                            {% trans "Company Values" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="value1Title" class="form-label">{% trans "Value 1 Title" %}</label>
                            <input type="text" class="form-control" id="value1Title" value="Innovation">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value1Desc" class="form-label">{% trans "Value 1 Description" %}</label>
                            <textarea class="form-control" id="value1Desc" rows="2">We embrace new ideas and approaches to solve complex challenges.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="value2Title" class="form-label">{% trans "Value 2 Title" %}</label>
                            <input type="text" class="form-control" id="value2Title" value="Collaboration">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value2Desc" class="form-label">{% trans "Value 2 Description" %}</label>
                            <textarea class="form-control" id="value2Desc" rows="2">We believe diverse perspectives create better solutions.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="value3Title" class="form-label">{% trans "Value 3 Title" %}</label>
                            <input type="text" class="form-control" id="value3Title" value="Excellence">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value3Desc" class="form-label">{% trans "Value 3 Description" %}</label>
                            <textarea class="form-control" id="value3Desc" rows="2">We strive for excellence in everything we do.</textarea>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showValues" checked>
                            <label class="form-check-label" for="showValues">
                                {% trans "Show Company Values Section" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Page Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateFullPage()">
                            <i class="fas fa-download me-2"></i>
                            {% trans "Download Complete Page" %}
                        </button>
                        <small class="form-text text-muted mt-2">{% trans "Downloads a ZIP file with HTML, CSS, and assets" %}</small>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Live Preview -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Live Preview" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('desktop')">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('tablet')">
                            <i class="fas fa-tablet-alt"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('mobile')">
                            <i class="fas fa-mobile-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="preview-container" id="previewContainer">
                    <div class="preview-frame" id="previewFrame">
                        <!-- Full page preview will be rendered here -->
                        <div id="pagePreview" class="page-preview">
                            <!-- This will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>