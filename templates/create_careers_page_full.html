{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - Customization Controls -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-file-code me-2"></i>{% trans "Full Page Builder" %}</h4>
                    <p class="text-muted">{% trans "Create a complete careers page" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- Company Information Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Information" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="Your Company">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Build what matters. Grow your career. Shape the future.">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyDescription" class="form-label">{% trans "Company Description" %}</label>
                            <textarea class="form-control" id="companyDescription" rows="3" placeholder="{% trans 'Describe your company' %}">We are a leading technology company focused on innovation and excellence.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                            <input type="file" class="form-control" id="logoUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 300x120px, PNG or JPG" %}</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="bannerUpload" class="form-label">{% trans "Header Banner" %}</label>
                            <input type="file" class="form-control" id="bannerUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 1200x400px, JPG or PNG" %}</small>
                        </div>
                    </div>

                    <!-- Design Customization Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design & Colors" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="secondaryColor" class="form-label">{% trans "Secondary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#6c757d">
                        </div>

                        <div class="form-group mb-3">
                            <label for="backgroundColor" class="form-label">{% trans "Background Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="backgroundColor" value="#f8f9fa">
                        </div>

                        <div class="form-group mb-3">
                            <label for="pageStyle" class="form-label">{% trans "Page Style" %}</label>
                            <select class="form-select" id="pageStyle">
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                                <option value="corporate">{% trans "Corporate" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Company Stats Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            {% trans "Company Statistics" %}
                        </h5>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="teamSize" class="form-label">{% trans "Team Size" %}</label>
                                    <input type="text" class="form-control" id="teamSize" placeholder="250+" value="250+">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="countries" class="form-label">{% trans "Countries" %}</label>
                                    <input type="text" class="form-control" id="countries" placeholder="15+" value="15+">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="satisfaction" class="form-label">{% trans "Satisfaction" %}</label>
                                    <input type="text" class="form-control" id="satisfaction" placeholder="98%" value="98%">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="openPositions" class="form-label">{% trans "Open Positions" %}</label>
                                    <input type="text" class="form-control" id="openPositions" placeholder="25+" value="25+">
                                </div>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showStats" checked>
                            <label class="form-check-label" for="showStats">
                                {% trans "Show Company Statistics" %}
                            </label>
                        </div>
                    </div>

                    <!-- Company Values Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-heart me-2"></i>
                            {% trans "Company Values" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="value1Title" class="form-label">{% trans "Value 1 Title" %}</label>
                            <input type="text" class="form-control" id="value1Title" value="Innovation">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value1Desc" class="form-label">{% trans "Value 1 Description" %}</label>
                            <textarea class="form-control" id="value1Desc" rows="2">We embrace new ideas and approaches to solve complex challenges.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="value2Title" class="form-label">{% trans "Value 2 Title" %}</label>
                            <input type="text" class="form-control" id="value2Title" value="Collaboration">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value2Desc" class="form-label">{% trans "Value 2 Description" %}</label>
                            <textarea class="form-control" id="value2Desc" rows="2">We believe diverse perspectives create better solutions.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="value3Title" class="form-label">{% trans "Value 3 Title" %}</label>
                            <input type="text" class="form-control" id="value3Title" value="Excellence">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value3Desc" class="form-label">{% trans "Value 3 Description" %}</label>
                            <textarea class="form-control" id="value3Desc" rows="2">We strive for excellence in everything we do.</textarea>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showValues" checked>
                            <label class="form-check-label" for="showValues">
                                {% trans "Show Company Values Section" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Page Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateFullPage()">
                            <i class="fas fa-download me-2"></i>
                            {% trans "Download Complete Page" %}
                        </button>
                        <small class="form-text text-muted mt-2">{% trans "Downloads a ZIP file with HTML, CSS, and assets" %}</small>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Live Preview -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Live Preview" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('desktop')">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('tablet')">
                            <i class="fas fa-tablet-alt"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('mobile')">
                            <i class="fas fa-mobile-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="preview-container" id="previewContainer">
                    <div class="preview-frame" id="previewFrame">
                        <!-- Full page preview will be rendered here -->
                        <div id="pagePreview" class="page-preview">
                            <!-- This will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.careers-builder-container {
    height: 100vh;
    overflow: hidden;
}

.builder-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.sidebar-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.panel-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-title {
    color: #343a40;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.preview-panel {
    background: #ffffff;
    height: 100vh;
    overflow: hidden;
    padding: 1.5rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.preview-controls .btn {
    margin-left: 0.5rem;
}

.preview-container {
    height: calc(100vh - 120px);
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    overflow: auto;
}

.preview-frame {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-height: 600px;
    transition: all 0.3s ease;
}

.preview-frame.mobile {
    max-width: 375px;
    margin: 0 auto;
}

.preview-frame.tablet {
    max-width: 768px;
    margin: 0 auto;
}

.page-preview {
    width: 100%;
    height: 100%;
}

.form-control-color {
    width: 100%;
    height: 38px;
}

@media (max-width: 992px) {
    .careers-builder-container {
        height: auto;
    }

    .builder-sidebar,
    .preview-panel {
        height: auto;
    }

    .preview-container {
        height: 500px;
    }
}
</style>

<script>
// Full page builder functionality
let pageConfig = {
    companyName: 'Your Company',
    tagline: 'Build what matters. Grow your career. Shape the future.',
    description: 'We are a leading technology company focused on innovation and excellence.',
    logo: null,
    banner: null,
    primaryColor: '#343a40',
    secondaryColor: '#6c757d',
    backgroundColor: '#f8f9fa',
    style: 'modern',
    teamSize: '250+',
    countries: '15+',
    satisfaction: '98%',
    openPositions: '25+',
    showStats: true,
    value1Title: 'Innovation',
    value1Desc: 'We embrace new ideas and approaches to solve complex challenges.',
    value2Title: 'Collaboration',
    value2Desc: 'We believe diverse perspectives create better solutions.',
    value3Title: 'Excellence',
    value3Desc: 'We strive for excellence in everything we do.',
    showValues: true
};

// Initialize the builder
document.addEventListener('DOMContentLoaded', function() {
    initializeFullPageBuilder();
    updateFullPagePreview();
});

function initializeFullPageBuilder() {
    // Add event listeners for all form controls
    document.getElementById('companyName').addEventListener('input', updateFullPageConfig);
    document.getElementById('companyTagline').addEventListener('input', updateFullPageConfig);
    document.getElementById('companyDescription').addEventListener('input', updateFullPageConfig);
    document.getElementById('logoUpload').addEventListener('change', handleLogoUpload);
    document.getElementById('bannerUpload').addEventListener('change', handleBannerUpload);
    document.getElementById('primaryColor').addEventListener('change', updateFullPageConfig);
    document.getElementById('secondaryColor').addEventListener('change', updateFullPageConfig);
    document.getElementById('backgroundColor').addEventListener('change', updateFullPageConfig);
    document.getElementById('pageStyle').addEventListener('change', updateFullPageConfig);
    document.getElementById('teamSize').addEventListener('input', updateFullPageConfig);
    document.getElementById('countries').addEventListener('input', updateFullPageConfig);
    document.getElementById('satisfaction').addEventListener('input', updateFullPageConfig);
    document.getElementById('openPositions').addEventListener('input', updateFullPageConfig);
    document.getElementById('showStats').addEventListener('change', updateFullPageConfig);
    document.getElementById('value1Title').addEventListener('input', updateFullPageConfig);
    document.getElementById('value1Desc').addEventListener('input', updateFullPageConfig);
    document.getElementById('value2Title').addEventListener('input', updateFullPageConfig);
    document.getElementById('value2Desc').addEventListener('input', updateFullPageConfig);
    document.getElementById('value3Title').addEventListener('input', updateFullPageConfig);
    document.getElementById('value3Desc').addEventListener('input', updateFullPageConfig);
    document.getElementById('showValues').addEventListener('change', updateFullPageConfig);
}

function updateFullPageConfig() {
    pageConfig.companyName = document.getElementById('companyName').value;
    pageConfig.tagline = document.getElementById('companyTagline').value;
    pageConfig.description = document.getElementById('companyDescription').value;
    pageConfig.primaryColor = document.getElementById('primaryColor').value;
    pageConfig.secondaryColor = document.getElementById('secondaryColor').value;
    pageConfig.backgroundColor = document.getElementById('backgroundColor').value;
    pageConfig.style = document.getElementById('pageStyle').value;
    pageConfig.teamSize = document.getElementById('teamSize').value;
    pageConfig.countries = document.getElementById('countries').value;
    pageConfig.satisfaction = document.getElementById('satisfaction').value;
    pageConfig.openPositions = document.getElementById('openPositions').value;
    pageConfig.showStats = document.getElementById('showStats').checked;
    pageConfig.value1Title = document.getElementById('value1Title').value;
    pageConfig.value1Desc = document.getElementById('value1Desc').value;
    pageConfig.value2Title = document.getElementById('value2Title').value;
    pageConfig.value2Desc = document.getElementById('value2Desc').value;
    pageConfig.value3Title = document.getElementById('value3Title').value;
    pageConfig.value3Desc = document.getElementById('value3Desc').value;
    pageConfig.showValues = document.getElementById('showValues').checked;

    updateFullPagePreview();
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            pageConfig.logo = e.target.result;
            updateFullPagePreview();
        };
        reader.readAsDataURL(file);
    }
}

function handleBannerUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            pageConfig.banner = e.target.result;
            updateFullPagePreview();
        };
        reader.readAsDataURL(file);
    }
}

function updateFullPagePreview() {
    renderFullPagePreview();
}

function renderFullPagePreview() {
    const previewContainer = document.getElementById('pagePreview');

    const statsSection = pageConfig.showStats ? `
        <div style="background: white; padding: 40px 20px; text-align: center;">
            <div style="display: flex; justify-content: space-around; flex-wrap: wrap; max-width: 800px; margin: 0 auto;">
                <div style="margin: 10px;">
                    <div style="font-size: 2rem; font-weight: bold; color: ${pageConfig.primaryColor};">${pageConfig.teamSize}</div>
                    <div style="color: ${pageConfig.secondaryColor};">Team Members</div>
                </div>
                <div style="margin: 10px;">
                    <div style="font-size: 2rem; font-weight: bold; color: ${pageConfig.primaryColor};">${pageConfig.countries}</div>
                    <div style="color: ${pageConfig.secondaryColor};">Countries</div>
                </div>
                <div style="margin: 10px;">
                    <div style="font-size: 2rem; font-weight: bold; color: ${pageConfig.primaryColor};">${pageConfig.satisfaction}</div>
                    <div style="color: ${pageConfig.secondaryColor};">Employee Satisfaction</div>
                </div>
                <div style="margin: 10px;">
                    <div style="font-size: 2rem; font-weight: bold; color: ${pageConfig.primaryColor};">${pageConfig.openPositions}</div>
                    <div style="color: ${pageConfig.secondaryColor};">Open Positions</div>
                </div>
            </div>
        </div>
    ` : '';

    const valuesSection = pageConfig.showValues ? `
        <div style="background: ${pageConfig.backgroundColor}; padding: 60px 20px;">
            <div style="max-width: 1200px; margin: 0 auto; text-align: center;">
                <h2 style="color: ${pageConfig.primaryColor}; margin-bottom: 40px; font-size: 2.5rem;">Our Values</h2>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 30px;">
                    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; flex: 1;">
                        <div style="font-size: 3rem; color: ${pageConfig.primaryColor}; margin-bottom: 20px;">💡</div>
                        <h3 style="color: ${pageConfig.primaryColor}; margin-bottom: 15px;">${pageConfig.value1Title}</h3>
                        <p style="color: ${pageConfig.secondaryColor}; line-height: 1.6;">${pageConfig.value1Desc}</p>
                    </div>
                    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; flex: 1;">
                        <div style="font-size: 3rem; color: ${pageConfig.primaryColor}; margin-bottom: 20px;">🤝</div>
                        <h3 style="color: ${pageConfig.primaryColor}; margin-bottom: 15px;">${pageConfig.value2Title}</h3>
                        <p style="color: ${pageConfig.secondaryColor}; line-height: 1.6;">${pageConfig.value2Desc}</p>
                    </div>
                    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 300px; flex: 1;">
                        <div style="font-size: 3rem; color: ${pageConfig.primaryColor}; margin-bottom: 20px;">⭐</div>
                        <h3 style="color: ${pageConfig.primaryColor}; margin-bottom: 15px;">${pageConfig.value3Title}</h3>
                        <p style="color: ${pageConfig.secondaryColor}; line-height: 1.6;">${pageConfig.value3Desc}</p>
                    </div>
                </div>
            </div>
        </div>
    ` : '';

    previewContainer.innerHTML = `
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; margin: 0; padding: 0;">
            <!-- Header Section -->
            <header style="background: linear-gradient(135deg, ${pageConfig.primaryColor}, ${pageConfig.secondaryColor}); color: white; padding: 80px 20px; text-align: center; position: relative; overflow: hidden;">
                ${pageConfig.banner ? `<div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('${pageConfig.banner}'); background-size: cover; background-position: center; opacity: 0.3;"></div>` : ''}
                <div style="position: relative; z-index: 2; max-width: 1200px; margin: 0 auto;">
                    ${pageConfig.logo ? `<img src="${pageConfig.logo}" alt="Logo" style="max-height: 80px; margin-bottom: 20px;">` : ''}
                    <h1 style="font-size: 3.5rem; font-weight: bold; margin: 0 0 20px 0;">Careers at ${pageConfig.companyName}</h1>
                    <p style="font-size: 1.5rem; margin: 0 0 40px 0; opacity: 0.9;">${pageConfig.tagline}</p>
                    <p style="font-size: 1.1rem; max-width: 600px; margin: 0 auto; opacity: 0.8;">${pageConfig.description}</p>
                </div>
            </header>

            ${statsSection}

            <!-- Jobs Section -->
            <div style="background: white; padding: 60px 20px;">
                <div style="max-width: 1200px; margin: 0 auto;">
                    <h2 style="color: ${pageConfig.primaryColor}; text-align: center; margin-bottom: 40px; font-size: 2.5rem;">Current Opportunities</h2>

                    <!-- Sample Job Listings -->
                    <div style="display: grid; gap: 20px; max-width: 800px; margin: 0 auto;">
                        <div style="border: 1px solid #e9ecef; border-radius: 12px; padding: 30px; transition: all 0.3s ease; background: white;">
                            <h3 style="color: ${pageConfig.primaryColor}; margin: 0 0 10px 0; font-size: 1.5rem;">Senior Software Engineer</h3>
                            <div style="display: flex; gap: 20px; margin-bottom: 15px; flex-wrap: wrap;">
                                <span style="color: ${pageConfig.secondaryColor}; font-size: 0.9rem;">📍 Remote</span>
                                <span style="color: ${pageConfig.secondaryColor}; font-size: 0.9rem;">💰 $80,000 - $120,000</span>
                                <span style="color: ${pageConfig.secondaryColor}; font-size: 0.9rem;">📅 Posted 2 days ago</span>
                            </div>
                            <p style="color: #666; margin: 0 0 20px 0; line-height: 1.6;">We're looking for a senior software engineer to join our growing team and help build the next generation of our platform.</p>
                            <button style="background: ${pageConfig.primaryColor}; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-weight: 600;">Apply Now</button>
                        </div>

                        <div style="border: 1px solid #e9ecef; border-radius: 12px; padding: 30px; transition: all 0.3s ease; background: white;">
                            <h3 style="color: ${pageConfig.primaryColor}; margin: 0 0 10px 0; font-size: 1.5rem;">Product Manager</h3>
                            <div style="display: flex; gap: 20px; margin-bottom: 15px; flex-wrap: wrap;">
                                <span style="color: ${pageConfig.secondaryColor}; font-size: 0.9rem;">📍 New York, NY</span>
                                <span style="color: ${pageConfig.secondaryColor}; font-size: 0.9rem;">💰 $90,000 - $130,000</span>
                                <span style="color: ${pageConfig.secondaryColor}; font-size: 0.9rem;">📅 Posted 1 week ago</span>
                            </div>
                            <p style="color: #666; margin: 0 0 20px 0; line-height: 1.6;">Join our product team and help shape the future of our products. We're looking for someone with strong analytical skills.</p>
                            <button style="background: ${pageConfig.primaryColor}; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-weight: 600;">Apply Now</button>
                        </div>
                    </div>
                </div>
            </div>

            ${valuesSection}

            <!-- Footer -->
            <footer style="background: ${pageConfig.primaryColor}; color: white; padding: 40px 20px; text-align: center;">
                <div style="max-width: 1200px; margin: 0 auto;">
                    <p style="margin: 0; opacity: 0.8;">© 2024 ${pageConfig.companyName}. All rights reserved.</p>
                    <p style="margin: 10px 0 0 0; opacity: 0.6; font-size: 0.9rem;">Powered by Workloupe</p>
                </div>
            </footer>
        </div>
    `;
}

function togglePreviewMode(mode) {
    const frame = document.getElementById('previewFrame');
    frame.className = 'preview-frame ' + mode;

    // Update button states
    document.querySelectorAll('.preview-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-secondary');
    });
    event.target.classList.remove('btn-outline-secondary');
    event.target.classList.add('btn-primary');
}

function generateFullPage() {
    // Generate the complete HTML page and download as ZIP
    const htmlContent = generateCompleteHTML();
    const cssContent = generateCSS();

    // Create ZIP file with HTML and CSS
    downloadPageAsZip(htmlContent, cssContent);
}

function generateCompleteHTML() {
    // Generate complete HTML based on career_page_initial_template.html structure
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers at ${pageConfig.companyName}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    ${document.getElementById('pagePreview').innerHTML}

    <script>
        // Add any necessary JavaScript for job loading
        console.log('Careers page loaded for ${pageConfig.companyName}');
    </script>
</body>
</html>`;
}

function generateCSS() {
    return `/* Generated CSS for ${pageConfig.companyName} Careers Page */
:root {
    --primary-color: ${pageConfig.primaryColor};
    --secondary-color: ${pageConfig.secondaryColor};
    --background-color: ${pageConfig.backgroundColor};
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    header h1 {
        font-size: 2.5rem !important;
    }

    header p {
        font-size: 1.2rem !important;
    }

    .stats-container {
        flex-direction: column !important;
    }

    .values-container {
        flex-direction: column !important;
    }
}`;
}

function downloadPageAsZip(htmlContent, cssContent) {
    // For now, just download the HTML file
    // In a real implementation, you'd use a library like JSZip
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${pageConfig.companyName.toLowerCase().replace(/\s+/g, '-')}-careers-page.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    // Also download CSS
    const cssBlob = new Blob([cssContent], { type: 'text/css' });
    const cssUrl = window.URL.createObjectURL(cssBlob);
    const cssA = document.createElement('a');
    cssA.href = cssUrl;
    cssA.download = 'styles.css';
    document.body.appendChild(cssA);
    cssA.click();
    document.body.removeChild(cssA);
    window.URL.revokeObjectURL(cssUrl);

    alert('{% trans "Files downloaded! Upload both HTML and CSS files to your web server." %}');
}
</script>
{% endblock %}