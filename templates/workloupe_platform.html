{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="workloupe-platform-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - Company Information Form -->
            <div class="col-lg-6 platform-form-panel">
                <div class="form-header">
                    <h4><i class="fas fa-globe me-2"></i>{% trans "Workloupe Platform Setup" %}</h4>
                    <p class="text-muted">{% trans "Create your company profile on workloupe.com" %}</p>
                </div>

                <form id="workloupePlatformForm" class="platform-form">
                    <!-- Basic Company Information -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Information" %}
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerName" class="form-label">{% trans "Company Name" %} *</label>
                                    <input type="text" class="form-control" id="employerName" name="employer_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerEmail" class="form-label">{% trans "Company Email" %} *</label>
                                    <input type="email" class="form-control" id="employerEmail" name="employer_email" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerPhone" class="form-label">{% trans "Phone Number" %}</label>
                                    <input type="tel" class="form-control" id="employerPhone" name="employer_phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerWebsite" class="form-label">{% trans "Website" %} *</label>
                                    <input type="url" class="form-control" id="employerWebsite" name="employer_website" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="employerAddress" class="form-label">{% trans "Company Address" %}</label>
                            <input type="text" class="form-control" id="employerAddress" name="employer_address">
                        </div>

                        <div class="form-group mb-3">
                            <label for="officeLocations" class="form-label">{% trans "Office Locations" %}</label>
                            <input type="text" class="form-control" id="officeLocations" name="office_locations" placeholder="{% trans 'e.g., New York, London, Remote' %}">
                        </div>

                        <div class="form-group mb-3">
                            <label for="employerDescription" class="form-label">{% trans "Company Description" %} *</label>
                            <textarea class="form-control" id="employerDescription" name="employer_description" rows="4" required placeholder="{% trans 'Describe your company, mission, and what makes it special...' %}"></textarea>
                        </div>
                    </div>

                    <!-- Company Details -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Company Details" %}
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerIndustry" class="form-label">{% trans "Industry" %}</label>
                                    <select class="form-select" id="employerIndustry" name="employer_industry">
                                        <option value="">{% trans "Select Industry" %}</option>
                                        <option value="Technology">{% trans "Technology" %}</option>
                                        <option value="Healthcare">{% trans "Healthcare" %}</option>
                                        <option value="Finance">{% trans "Finance" %}</option>
                                        <option value="Education">{% trans "Education" %}</option>
                                        <option value="Manufacturing">{% trans "Manufacturing" %}</option>
                                        <option value="Retail">{% trans "Retail" %}</option>
                                        <option value="Consulting">{% trans "Consulting" %}</option>
                                        <option value="Other">{% trans "Other" %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employerHeadcount" class="form-label">{% trans "Company Size" %}</label>
                                    <select class="form-select" id="employerHeadcount" name="employer_headcount">
                                        <option value="">{% trans "Select Size" %}</option>
                                        <option value="1-10">1-10 {% trans "employees" %}</option>
                                        <option value="11-50">11-50 {% trans "employees" %}</option>
                                        <option value="51-200">51-200 {% trans "employees" %}</option>
                                        <option value="201-500">201-500 {% trans "employees" %}</option>
                                        <option value="501-1000">501-1000 {% trans "employees" %}</option>
                                        <option value="1000+">1000+ {% trans "employees" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="employerSocialPortals" class="form-label">{% trans "Social Media Links" %}</label>
                            <textarea class="form-control" id="employerSocialPortals" name="employer_social_portals" rows="3" placeholder="{% trans 'LinkedIn: https://linkedin.com/company/yourcompany\nTwitter: https://twitter.com/yourcompany\nFacebook: https://facebook.com/yourcompany' %}"></textarea>
                            <small class="form-text text-muted">{% trans "Enter one social media link per line" %}</small>
                        </div>
                    </div>

                    <!-- Logo and Banner Upload -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-image me-2"></i>
                            {% trans "Branding Assets" %}
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                                    <input type="file" class="form-control" id="logoUpload" name="logo" accept="image/*">
                                    <small class="form-text text-muted">{% trans "Recommended: 300x120px, PNG or JPG" %}</small>
                                    <div id="logoPreview" class="image-preview mt-2"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="bannerUpload" class="form-label">{% trans "Company Banner" %}</label>
                                    <input type="file" class="form-control" id="bannerUpload" name="banner" accept="image/*">
                                    <small class="form-text text-muted">{% trans "Recommended: 1200x400px, JPG or PNG" %}</small>
                                    <div id="bannerPreview" class="image-preview mt-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Company Gallery -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-images me-2"></i>
                            {% trans "Company Gallery" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="galleryUpload" class="form-label">{% trans "Upload Photos" %}</label>
                            <input type="file" class="form-control" id="galleryUpload" name="gallery[]" accept="image/*" multiple>
                            <small class="form-text text-muted">{% trans "Upload multiple photos to showcase your company culture, office, team, etc." %}</small>
                        </div>
                        
                        <div id="galleryPreview" class="gallery-preview">
                            <!-- Gallery images will be displayed here -->
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>
                            {% trans "Reset" %}
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save & Publish Profile" %}
                        </button>
                    </div>
                </form>
            </div>

            <!-- Right Panel - Preview -->
            <div class="col-lg-6 platform-preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Profile Preview" %}</h4>
                    <p class="text-muted">{% trans "This is how your company profile will appear on workloupe.com" %}</p>
                </div>

                <div class="preview-container">
                    <div class="profile-preview" id="profilePreview">
                        <!-- Company profile preview will be rendered here -->
                        <div class="preview-placeholder">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{% trans "Fill out the form to see your profile preview" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="successModalLabel">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    {% trans "Profile Created Successfully!" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "Your company profile has been created and published on workloupe.com." %}</p>
                <div class="alert alert-info">
                    <strong>{% trans "Your Profile URL:" %}</strong><br>
                    <a href="#" id="profileUrl" target="_blank" class="text-decoration-none"></a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <a href="#" id="viewProfileBtn" class="btn btn-primary" target="_blank">
                    <i class="fas fa-external-link-alt me-2"></i>
                    {% trans "View Profile" %}
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.workloupe-platform-container {
    min-height: 100vh;
    background: #f8f9fa;
}

.platform-form-panel {
    background: white;
    padding: 2rem;
    border-right: 1px solid #dee2e6;
    max-height: 100vh;
    overflow-y: auto;
}

.platform-preview-panel {
    background: #f8f9fa;
    padding: 2rem;
    max-height: 100vh;
    overflow-y: auto;
}

.form-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.section-title {
    color: #343a40;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.form-actions {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    text-align: right;
    margin-top: 2rem;
}

.form-actions .btn {
    margin-left: 1rem;
}

.image-preview {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
}

.gallery-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.gallery-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-item .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 0.8rem;
    cursor: pointer;
}

.preview-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.preview-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-height: 600px;
}

.profile-preview {
    padding: 2rem;
}

.preview-placeholder {
    text-align: center;
    padding: 4rem 2rem;
}

.company-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.company-logo {
    max-height: 80px;
    margin-bottom: 1rem;
}

.company-banner {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.company-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.info-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.info-value {
    color: #212529;
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.social-link {
    color: #6c757d;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: #007bff;
}

.company-gallery-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.gallery-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-image:hover {
    transform: scale(1.05);
}

@media (max-width: 992px) {
    .platform-form-panel,
    .platform-preview-panel {
        max-height: none;
        padding: 1rem;
    }

    .form-actions {
        text-align: center;
    }

    .form-actions .btn {
        margin: 0.5rem;
        width: 100%;
        max-width: 200px;
    }
}
</style>

<script>
// Workloupe platform functionality
let platformData = {
    employerName: '',
    employerEmail: '',
    employerPhone: '',
    employerWebsite: '',
    employerAddress: '',
    officeLocations: '',
    employerDescription: '',
    employerIndustry: '',
    employerHeadcount: '',
    employerSocialPortals: '',
    logoUrl: null,
    bannerUrl: null,
    galleryImages: []
};

// Initialize the platform builder
document.addEventListener('DOMContentLoaded', function() {
    initializePlatformBuilder();
    updatePreview();
});

function initializePlatformBuilder() {
    // Add event listeners for all form controls
    const formInputs = [
        'employerName', 'employerEmail', 'employerPhone', 'employerWebsite',
        'employerAddress', 'officeLocations', 'employerDescription',
        'employerIndustry', 'employerHeadcount', 'employerSocialPortals'
    ];

    formInputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', updatePlatformData);
            element.addEventListener('change', updatePlatformData);
        }
    });

    // File upload handlers
    document.getElementById('logoUpload').addEventListener('change', handleLogoUpload);
    document.getElementById('bannerUpload').addEventListener('change', handleBannerUpload);
    document.getElementById('galleryUpload').addEventListener('change', handleGalleryUpload);

    // Form submission
    document.getElementById('workloupePlatformForm').addEventListener('submit', handleFormSubmission);
}

function updatePlatformData() {
    platformData.employerName = document.getElementById('employerName').value;
    platformData.employerEmail = document.getElementById('employerEmail').value;
    platformData.employerPhone = document.getElementById('employerPhone').value;
    platformData.employerWebsite = document.getElementById('employerWebsite').value;
    platformData.employerAddress = document.getElementById('employerAddress').value;
    platformData.officeLocations = document.getElementById('officeLocations').value;
    platformData.employerDescription = document.getElementById('employerDescription').value;
    platformData.employerIndustry = document.getElementById('employerIndustry').value;
    platformData.employerHeadcount = document.getElementById('employerHeadcount').value;
    platformData.employerSocialPortals = document.getElementById('employerSocialPortals').value;

    updatePreview();
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            platformData.logoUrl = e.target.result;
            document.getElementById('logoPreview').innerHTML =
                `<img src="${e.target.result}" alt="Logo Preview">`;
            updatePreview();
        };
        reader.readAsDataURL(file);
    }
}

function handleBannerUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            platformData.bannerUrl = e.target.result;
            document.getElementById('bannerPreview').innerHTML =
                `<img src="${e.target.result}" alt="Banner Preview">`;
            updatePreview();
        };
        reader.readAsDataURL(file);
    }
}

function handleGalleryUpload(event) {
    const files = Array.from(event.target.files);
    files.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            platformData.galleryImages.push(e.target.result);
            updateGalleryPreview();
            updatePreview();
        };
        reader.readAsDataURL(file);
    });
}

function updateGalleryPreview() {
    const galleryPreview = document.getElementById('galleryPreview');
    galleryPreview.innerHTML = '';

    platformData.galleryImages.forEach((imageUrl, index) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.innerHTML = `
            <img src="${imageUrl}" alt="Gallery Image ${index + 1}">
            <button class="remove-btn" onclick="removeGalleryImage(${index})">×</button>
        `;
        galleryPreview.appendChild(galleryItem);
    });
}

function removeGalleryImage(index) {
    platformData.galleryImages.splice(index, 1);
    updateGalleryPreview();
    updatePreview();
}

function updatePreview() {
    renderProfilePreview();
}

function renderProfilePreview() {
    const previewContainer = document.getElementById('profilePreview');

    if (!platformData.employerName && !platformData.employerDescription) {
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <p class="text-muted">{% trans "Fill out the form to see your profile preview" %}</p>
            </div>
        `;
        return;
    }

    const socialLinks = parseSocialLinks(platformData.employerSocialPortals);
    const socialLinksHtml = socialLinks.map(link =>
        `<a href="${link.url}" class="social-link" target="_blank">
            <i class="${getSocialIcon(link.platform)}"></i>
        </a>`
    ).join('');

    const galleryHtml = platformData.galleryImages.length > 0 ? `
        <div class="company-gallery-section">
            <h5><i class="fas fa-images me-2"></i>{% trans "Company Gallery" %}</h5>
            <div class="gallery-grid">
                ${platformData.galleryImages.map(imageUrl =>
                    `<img src="${imageUrl}" alt="Company Photo" class="gallery-image">`
                ).join('')}
            </div>
        </div>
    ` : '';

    previewContainer.innerHTML = `
        <div class="company-header">
            ${platformData.bannerUrl ? `<img src="${platformData.bannerUrl}" alt="Company Banner" class="company-banner">` : ''}
            ${platformData.logoUrl ? `<img src="${platformData.logoUrl}" alt="Company Logo" class="company-logo">` : ''}
            <h2 style="color: #343a40; margin: 1rem 0;">${platformData.employerName || 'Company Name'}</h2>
            <p style="color: #6c757d; font-size: 1.1rem; margin: 0;">${platformData.employerDescription || 'Company description will appear here...'}</p>
            ${socialLinksHtml ? `<div class="social-links">${socialLinksHtml}</div>` : ''}
        </div>

        <div class="company-info-grid">
            ${platformData.employerWebsite ? `
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-globe me-2"></i>{% trans "Website" %}</div>
                    <div class="info-value"><a href="${platformData.employerWebsite}" target="_blank">${platformData.employerWebsite}</a></div>
                </div>
            ` : ''}

            ${platformData.employerIndustry ? `
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-industry me-2"></i>{% trans "Industry" %}</div>
                    <div class="info-value">${platformData.employerIndustry}</div>
                </div>
            ` : ''}

            ${platformData.employerHeadcount ? `
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-users me-2"></i>{% trans "Company Size" %}</div>
                    <div class="info-value">${platformData.employerHeadcount}</div>
                </div>
            ` : ''}

            ${platformData.employerAddress ? `
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-map-marker-alt me-2"></i>{% trans "Address" %}</div>
                    <div class="info-value">${platformData.employerAddress}</div>
                </div>
            ` : ''}

            ${platformData.officeLocations ? `
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-building me-2"></i>{% trans "Office Locations" %}</div>
                    <div class="info-value">${platformData.officeLocations}</div>
                </div>
            ` : ''}

            ${platformData.employerPhone ? `
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-phone me-2"></i>{% trans "Phone" %}</div>
                    <div class="info-value">${platformData.employerPhone}</div>
                </div>
            ` : ''}
        </div>

        ${galleryHtml}
    `;
}

function parseSocialLinks(socialText) {
    if (!socialText) return [];

    const lines = socialText.split('\n').filter(line => line.trim());
    return lines.map(line => {
        const parts = line.split(':');
        if (parts.length >= 2) {
            const platform = parts[0].trim().toLowerCase();
            const url = parts.slice(1).join(':').trim();
            return { platform, url };
        }
        return null;
    }).filter(Boolean);
}

function getSocialIcon(platform) {
    const icons = {
        'linkedin': 'fab fa-linkedin',
        'twitter': 'fab fa-twitter',
        'facebook': 'fab fa-facebook',
        'instagram': 'fab fa-instagram',
        'youtube': 'fab fa-youtube',
        'github': 'fab fa-github'
    };
    return icons[platform] || 'fas fa-link';
}

function resetForm() {
    if (confirm('{% trans "Are you sure you want to reset the form? All data will be lost." %}')) {
        document.getElementById('workloupePlatformForm').reset();
        platformData = {
            employerName: '',
            employerEmail: '',
            employerPhone: '',
            employerWebsite: '',
            employerAddress: '',
            officeLocations: '',
            employerDescription: '',
            employerIndustry: '',
            employerHeadcount: '',
            employerSocialPortals: '',
            logoUrl: null,
            bannerUrl: null,
            galleryImages: []
        };
        document.getElementById('logoPreview').innerHTML = '';
        document.getElementById('bannerPreview').innerHTML = '';
        document.getElementById('galleryPreview').innerHTML = '';
        updatePreview();
    }
}

function handleFormSubmission(event) {
    event.preventDefault();

    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Creating Profile..." %}';
    submitBtn.disabled = true;

    // Prepare form data
    const formData = new FormData();

    // Add text fields
    Object.keys(platformData).forEach(key => {
        if (typeof platformData[key] === 'string' && platformData[key]) {
            formData.append(key, platformData[key]);
        }
    });

    // Add files
    const logoFile = document.getElementById('logoUpload').files[0];
    const bannerFile = document.getElementById('bannerUpload').files[0];
    const galleryFiles = document.getElementById('galleryUpload').files;

    if (logoFile) formData.append('logo', logoFile);
    if (bannerFile) formData.append('banner', bannerFile);

    for (let i = 0; i < galleryFiles.length; i++) {
        formData.append('gallery[]', galleryFiles[i]);
    }

    // Submit to backend
    fetch('/create-workloupe-profile/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal
            const profileUrl = `https://workloupe.com/company/${data.company_slug}`;
            document.getElementById('profileUrl').href = profileUrl;
            document.getElementById('profileUrl').textContent = profileUrl;
            document.getElementById('viewProfileBtn').href = profileUrl;

            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
        } else {
            alert('{% trans "Error creating profile: " %}' + (data.error || '{% trans "Unknown error" %}'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{% trans "Error creating profile. Please try again." %}');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}
</script>
{% endblock %}
