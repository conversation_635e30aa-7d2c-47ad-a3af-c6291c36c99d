{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-page-setup container">
    <div class="header">
        <h1>{% trans "Career Page Setup" %}</h1>
        <p>{% trans "Choose the integration method that best suits your needs" %}</p>
    </div>

    <div class="integration-options">
        <!-- Option 1: RSS Feed -->
        <div class="option-card" onclick="showRSSModal()">
            <div class="option-icon"><i class="fas fa-rss"></i></div>
            <h3>{% trans "RSS Feed Integration" %}</h3>
            <p>{% trans "Already have your own careers page? Get our RSS feed to sync job listings" %}</p>
            <button class="btn-primary">{% trans "Get RSS Feed" %}</button>
        </div>

        <!-- Option 2: Full HTML Page -->
        <div class="option-card" onclick="window.location.href='{% url 'create_careers_page_full' %}'">
            <div class="option-icon"><i class="fas fa-file-code"></i></div>
            <h3>{% trans "Full HTML Page" %}</h3>
            <p>{% trans "Let us manage your entire careers page with our professional template" %}</p>
            <button class="btn-primary">{% trans "Create Page" %}</button>
        </div>

        <!-- Option 3: Custom HTML Widget -->
        <div class="option-card" onclick="window.location.href='{% url 'create_careers_widget' %}'">
            <div class="option-icon"><i class="fas fa-puzzle-piece"></i></div>
            <h3>{% trans "Custom HTML Widget" %}</h3>
            <p>{% trans "Perfect for drag & drop website builders like Wix" %}</p>
            <button class="btn-primary">{% trans "Get Widget Code" %}</button>
        </div>

        <!-- Option 4: WordPress Plugin -->
        <div class="option-card" onclick="window.location.href='{% url 'wordpress_integration' %}'">
            <div class="option-icon"><i class="fab fa-wordpress"></i></div>
            <h3>{% trans "WordPress Plugin" %}</h3>
            <p>{% trans "Seamless integration with your WordPress site" %}</p>
            <button class="btn-primary">{% trans "Setup WordPress" %}</button>
        </div>

        <!-- Option 5: Workloupe Platform -->
        <div class="option-card" onclick="window.location.href='{% url 'workloupe_platform' %}'">
            <div class="option-icon"><i class="fas fa-globe"></i></div>
            <h3>{% trans "Workloupe Platform" %}</h3>
            <p>{% trans "Use our platform as your company's career page" %}</p>
            <button class="btn-primary">{% trans "Setup Platform" %}</button>
        </div>
    </div>
</div>

<!-- RSS Feed Modal -->
<div id="rssModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>{% trans "Your RSS Feed URL" %}</h2>
        <div class="rss-url-container">
            <input type="text" id="rssUrl" value="https://workloupe.com/{{ employer_name }}/jobs.rss" readonly>
            <button onclick="copyRSSUrl()" class="copy-btn">
                <i class="fas fa-copy"></i>
                {% trans "Copy" %}
            </button>
        </div>
    </div>
</div>

<script>
    function showRSSModal() {
        document.getElementById('rssModal').style.display = 'block';
    }

    function copyRSSUrl() {
        const urlInput = document.getElementById('rssUrl');
        urlInput.select();
        document.execCommand('copy');
        alert('RSS URL copied to clipboard!');
    }

    // Close modal when clicking the X or outside the modal
    document.querySelector('.close').onclick = function() {
        document.getElementById('rssModal').style.display = 'none';
    }

    window.onclick = function(event) {
        const modal = document.getElementById('rssModal');
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    }
</script>
{% endblock %}
