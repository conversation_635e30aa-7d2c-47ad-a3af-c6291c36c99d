{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - Customization Controls -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-puzzle-piece me-2"></i>{% trans "Widget Builder" %}</h4>
                    <p class="text-muted">{% trans "Customize your careers widget" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- Company Branding Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Branding" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="Your Company">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Join our amazing team">
                        </div>

                        <div class="form-group mb-3">
                            <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                            <input type="file" class="form-control" id="logoUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 200x80px, PNG or JPG" %}</small>
                        </div>
                    </div>

                    <!-- Design Customization Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design & Colors" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="backgroundColor" class="form-label">{% trans "Background Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="backgroundColor" value="#ffffff">
                        </div>

                        <div class="form-group mb-3">
                            <label for="textColor" class="form-label">{% trans "Text Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="textColor" value="#333333">
                        </div>

                        <div class="form-group mb-3">
                            <label for="widgetStyle" class="form-label">{% trans "Widget Style" %}</label>
                            <select class="form-select" id="widgetStyle">
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Content Settings Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-cog me-2"></i>
                            {% trans "Content Settings" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="maxJobs" class="form-label">{% trans "Max Jobs to Display" %}</label>
                            <select class="form-select" id="maxJobs">
                                <option value="3">3 {% trans "jobs" %}</option>
                                <option value="5" selected>5 {% trans "jobs" %}</option>
                                <option value="10">10 {% trans "jobs" %}</option>
                                <option value="all">{% trans "All jobs" %}</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showSalary" checked>
                            <label class="form-check-label" for="showSalary">
                                {% trans "Show Salary Information" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showLocation" checked>
                            <label class="form-check-label" for="showLocation">
                                {% trans "Show Job Location" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showDate" checked>
                            <label class="form-check-label" for="showDate">
                                {% trans "Show Posted Date" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Widget Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateWidget()">
                            <i class="fas fa-code me-2"></i>
                            {% trans "Generate Widget Code" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Live Preview -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Live Preview" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('desktop')">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('tablet')">
                            <i class="fas fa-tablet-alt"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('mobile')">
                            <i class="fas fa-mobile-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="preview-container" id="previewContainer">
                    <div class="preview-frame" id="previewFrame">
                        <!-- Widget preview will be rendered here -->
                        <div id="widgetPreview" class="widget-preview">
                            <!-- This will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Widget Code Modal -->
<div class="modal fade" id="widgetCodeModal" tabindex="-1" aria-labelledby="widgetCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="widgetCodeModalLabel">
                    <i class="fas fa-code me-2"></i>
                    {% trans "Your Widget Code" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Copy this code and paste it into your website where you want the careers widget to appear." %}
                </div>
                
                <div class="code-container">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label for="widgetCode" class="form-label mb-0">{% trans "HTML Widget Code" %}</label>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyWidgetCode()">
                            <i class="fas fa-copy me-1"></i>
                            {% trans "Copy Code" %}
                        </button>
                    </div>
                    <textarea class="form-control code-textarea" id="widgetCode" rows="15" readonly></textarea>
                </div>

                <div class="mt-4">
                    <h6>{% trans "Integration Instructions:" %}</h6>
                    <ol class="small text-muted">
                        <li>{% trans "Copy the HTML code above" %}</li>
                        <li>{% trans "Paste it into your website's HTML where you want the widget to appear" %}</li>
                        <li>{% trans "The widget will automatically load your latest job postings" %}</li>
                        <li>{% trans "The widget is responsive and will adapt to your website's layout" %}</li>
                    </ol>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary" onclick="downloadWidget()">
                    <i class="fas fa-download me-2"></i>
                    {% trans "Download as HTML File" %}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.careers-builder-container {
    height: 100vh;
    overflow: hidden;
}

.builder-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.sidebar-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.panel-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-title {
    color: #343a40;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.preview-panel {
    background: #ffffff;
    height: 100vh;
    overflow: hidden;
    padding: 1.5rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.preview-controls .btn {
    margin-left: 0.5rem;
}

.preview-container {
    height: calc(100vh - 120px);
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    overflow: auto;
}

.preview-frame {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-height: 400px;
    transition: all 0.3s ease;
}

.preview-frame.mobile {
    max-width: 375px;
    margin: 0 auto;
}

.preview-frame.tablet {
    max-width: 768px;
    margin: 0 auto;
}

.widget-preview {
    padding: 2rem;
}

.code-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.form-control-color {
    width: 100%;
    height: 38px;
}

@media (max-width: 992px) {
    .careers-builder-container {
        height: auto;
    }
    
    .builder-sidebar,
    .preview-panel {
        height: auto;
    }
    
    .preview-container {
        height: 500px;
    }
}
</style>

<script>
// Widget builder functionality will be added here
let widgetConfig = {
    companyName: 'Your Company',
    tagline: 'Join our amazing team',
    logo: null,
    primaryColor: '#343a40',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    style: 'modern',
    maxJobs: 5,
    showSalary: true,
    showLocation: true,
    showDate: true
};

// Initialize the builder
document.addEventListener('DOMContentLoaded', function() {
    initializeBuilder();
    updatePreview();
});

function initializeBuilder() {
    // Add event listeners for all form controls
    document.getElementById('companyName').addEventListener('input', updateConfig);
    document.getElementById('companyTagline').addEventListener('input', updateConfig);
    document.getElementById('logoUpload').addEventListener('change', handleLogoUpload);
    document.getElementById('primaryColor').addEventListener('change', updateConfig);
    document.getElementById('backgroundColor').addEventListener('change', updateConfig);
    document.getElementById('textColor').addEventListener('change', updateConfig);
    document.getElementById('widgetStyle').addEventListener('change', updateConfig);
    document.getElementById('maxJobs').addEventListener('change', updateConfig);
    document.getElementById('showSalary').addEventListener('change', updateConfig);
    document.getElementById('showLocation').addEventListener('change', updateConfig);
    document.getElementById('showDate').addEventListener('change', updateConfig);
}

function updateConfig() {
    widgetConfig.companyName = document.getElementById('companyName').value;
    widgetConfig.tagline = document.getElementById('companyTagline').value;
    widgetConfig.primaryColor = document.getElementById('primaryColor').value;
    widgetConfig.backgroundColor = document.getElementById('backgroundColor').value;
    widgetConfig.textColor = document.getElementById('textColor').value;
    widgetConfig.style = document.getElementById('widgetStyle').value;
    widgetConfig.maxJobs = document.getElementById('maxJobs').value;
    widgetConfig.showSalary = document.getElementById('showSalary').checked;
    widgetConfig.showLocation = document.getElementById('showLocation').checked;
    widgetConfig.showDate = document.getElementById('showDate').checked;
    
    updatePreview();
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            widgetConfig.logo = e.target.result;
            updatePreview();
        };
        reader.readAsDataURL(file);
    }
}

function updatePreview() {
    // This function will render the widget preview
    // Implementation will be added in the next part
    renderWidgetPreview();
}

function renderWidgetPreview() {
    // Widget preview rendering logic will be implemented here
    const previewContainer = document.getElementById('widgetPreview');
    
    // Sample preview content
    previewContainer.innerHTML = `
        <div style="background: ${widgetConfig.backgroundColor}; color: ${widgetConfig.textColor}; padding: 20px; border-radius: 8px;">
            <div style="text-align: center; margin-bottom: 20px;">
                ${widgetConfig.logo ? `<img src="${widgetConfig.logo}" alt="Logo" style="max-height: 60px; margin-bottom: 10px;">` : ''}
                <h3 style="color: ${widgetConfig.primaryColor}; margin: 0;">${widgetConfig.companyName}</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.8;">${widgetConfig.tagline}</p>
            </div>
            <div style="border-top: 1px solid #eee; padding-top: 20px;">
                <h4 style="color: ${widgetConfig.primaryColor}; margin-bottom: 15px;">Current Opportunities</h4>
                <div style="space-y: 10px;">
                    <!-- Sample job listings will be rendered here -->
                    <div style="border: 1px solid #eee; padding: 15px; border-radius: 6px; margin-bottom: 10px;">
                        <h5 style="margin: 0 0 5px 0; color: ${widgetConfig.primaryColor};">Senior Developer</h5>
                        ${widgetConfig.showLocation ? '<p style="margin: 0; font-size: 0.9em; opacity: 0.7;">📍 Remote</p>' : ''}
                        ${widgetConfig.showSalary ? '<p style="margin: 0; font-size: 0.9em; opacity: 0.7;">💰 $80,000 - $120,000</p>' : ''}
                        ${widgetConfig.showDate ? '<p style="margin: 0; font-size: 0.9em; opacity: 0.7;">📅 Posted 2 days ago</p>' : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function togglePreviewMode(mode) {
    const frame = document.getElementById('previewFrame');
    frame.className = 'preview-frame ' + mode;
    
    // Update button states
    document.querySelectorAll('.preview-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-secondary');
    });
    event.target.classList.remove('btn-outline-secondary');
    event.target.classList.add('btn-primary');
}

function generateWidget() {
    // Generate the widget code and show modal
    const widgetCode = generateWidgetCode();
    document.getElementById('widgetCode').value = widgetCode;
    
    const modal = new bootstrap.Modal(document.getElementById('widgetCodeModal'));
    modal.show();
}

function generateWidgetCode() {
    // This will generate the actual widget HTML code
    return `<!-- Workloupe Careers Widget -->
<div id="workloupe-careers-widget" style="font-family: Arial, sans-serif;">
    <!-- Widget content will be loaded here -->
</div>

<script>
(function() {
    // Widget configuration
    const config = ${JSON.stringify(widgetConfig, null, 2)};
    
    // Load widget content
    function loadWidget() {
        // Implementation for loading actual job data
        console.log('Loading Workloupe careers widget with config:', config);
    }
    
    // Initialize widget
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadWidget);
    } else {
        loadWidget();
    }
})();
</script>`;
}

function copyWidgetCode() {
    const codeTextarea = document.getElementById('widgetCode');
    codeTextarea.select();
    codeTextarea.setSelectionRange(0, 99999);
    
    try {
        document.execCommand('copy');
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>{% trans "Copied!" %}';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        alert('{% trans "Failed to copy. Please copy manually." %}');
    }
}

function downloadWidget() {
    const widgetCode = document.getElementById('widgetCode').value;
    const blob = new Blob([widgetCode], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'workloupe-careers-widget.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
